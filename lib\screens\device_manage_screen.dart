import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/device_provider.dart';
import '../services/device_connect_auto_service.dart';
import '../services/device_service.dart';
import '../constants/constants.dart';
import '../widgets/battery_animation.dart';
import '../widgets/custom_time_picker_dialog.dart';
import '../screens/audio_record_screen.dart';

class DeviceManageScreen extends StatefulWidget {
  @override
  _DeviceManageScreenState createState() => _DeviceManageScreenState();
}

class _DeviceManageScreenState extends State<DeviceManageScreen> {
  bool _sleepModeEnabled = false;
  TimeOfDay _sleepStartTime = TimeOfDay(hour: 0, minute: 0);
  TimeOfDay _sleepEndTime = TimeOfDay(hour: 6, minute: 0);
  bool _isUpdating = false;

  // 保存Provider引用以避免在widget销毁后访问context
  DeviceProvider? _deviceProvider;
  DeviceApiService? _deviceApiService;
  DeviceConnectAutoService? _deviceConnectAutoService;

  @override
  void initState() {
    super.initState();
    _loadDeviceSleepSettings();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 在这里安全地获取Provider引用
    _deviceProvider = Provider.of<DeviceProvider>(context, listen: false);
    _deviceApiService = DeviceApiService(Provider.of(context, listen: false));
    _deviceConnectAutoService =
        Provider.of<DeviceConnectAutoService>(context, listen: false);
  }

  bool hasDevice = false;
  // 加载设备的休眠设置
  void _loadDeviceSleepSettings() {
    // 在didChangeDependencies中设置Provider引用后再调用此方法
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_deviceProvider?.device != null && mounted) {
        setState(() {
          _sleepStartTime = _deviceProvider!.device!.sleepStartTime;
          _sleepEndTime = _deviceProvider!.device!.sleepEndTime;
          // 根据实际设备状态设置休眠模式开关
          _sleepModeEnabled = _deviceProvider!.device!.nightSleepMode;
          hasDevice = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('设备管理'),
      ),
      backgroundColor: Colors.grey[200],
      body: hasDevice ? _buildDeviceManagementUI() : _buildNoDeviceUI(),
      floatingActionButton: hasDevice
          ? null
          : FloatingActionButton(
              onPressed: () {
                Navigator.pushNamed(context, Routes.deviceScan);
              },
              child: Icon(Icons.add),
              tooltip: '添加设备',
            ),
    );
  }

  Widget _buildNoDeviceUI() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '没有已连接的设备',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 10),
          Text(
            '点击右下角加号添加设备',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceManagementUI() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTopCardsRow(),
          SizedBox(height: 16),
          _buildSleepModeCard(),
          SizedBox(height: 16),
          _buildVoiceCallCard(),
          SizedBox(height: 16),
          _buildWifiSettingsCard(),
          SizedBox(height: 16),
          _buildUltrasonicWormCard(),
        ],
      ),
    );
  }

  // 第一排两个卡片：防丢码和设备电量
  Widget _buildTopCardsRow() {
    return Row(
      children: [
        Expanded(
          child: _buildAntiLostCodeCard(),
        ),
        SizedBox(width: 16),
        Expanded(
          child: _buildBatteryCard(),
        ),
      ],
    );
  }

  // 防丢码卡片
  Widget _buildAntiLostCodeCard() {
    return GestureDetector(
      onTap: () {
        Navigator.pushNamed(context, Routes.qrCode);
      },
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          padding: EdgeInsets.all(16),
          height: 150,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.qr_code,
                size: 50,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(height: 10),
              Text(
                '防丢码',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 5),
              Text(
                '管理防丢二维码',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 电池电量卡片
  Widget _buildBatteryCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        padding: EdgeInsets.all(16),
        height: 150,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              height: 80,
              width: 80,
              child: Selector<DeviceProvider, int>(
                selector: (context, provider) =>
                    provider.device?.batteryPercent ?? 20,
                builder: (context, batteryLevel, child) {
                  return BatteryAnimation(
                    batteryLevel: batteryLevel,
                  );
                },
              ),
            ),
            SizedBox(height: 10),
            Text(
              '设备电量',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  //项圈的网络连接状态
  Widget _buildWifiConnectingsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        padding: EdgeInsets.all(16),
        height: 150,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
          ],
        ),
      ),
    );
  }

  // 驱虫超声波卡片
  Widget _buildUltrasonicWormCard() {
    return GestureDetector(
      onTap: () {
        Navigator.pushNamed(context, Routes.ultrasonicConfig);
      },
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          padding: EdgeInsets.all(20),
          child: Row(
            children: [
              Icon(
                Icons.waves,
                size: 40,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '驱虫超声波',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 5),
                    Text(
                      '配置超声波参数',
                      style: TextStyle(
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 16),
            ],
          ),
        ),
      ),
    );
  }

  // 设备夜间休眠卡片
  Widget _buildSleepModeCard() {
    return GestureDetector(
      onTap: () {
        _showTimePicker();
      },
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          padding: EdgeInsets.all(20),
          child: Row(
            children: [
              Icon(
                Icons.nights_stay,
                size: 40,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '夜间休眠',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Row(
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '开始时间: ${_sleepStartTime.format(context)}',
                              style: TextStyle(
                                color: Colors.grey[700],
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              '结束时间: ${_sleepEndTime.format(context)}',
                              style: TextStyle(
                                color: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              _isUpdating
                  ? SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).primaryColor,
                        ),
                      ),
                    )
                  : Switch(
                      value: _sleepModeEnabled,
                      onChanged: _updateSleepModeState,
                      activeColor: Theme.of(context).primaryColor,
                    ),
            ],
          ),
        ),
      ),
    );
  }

  // 更新设备休眠模式状态
  void _updateSleepModeState(bool value) async {
    if (_isUpdating || !mounted) return;

    setState(() {
      _isUpdating = true;
    });

    if (_deviceProvider?.device == null || _deviceApiService == null) {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
      return;
    }

    bool success = false;
    try {
      // 通过设置休眠开始时间大于休眠结束时间，来判断是否开启休眠模式
      if (value) {
        success = await _deviceApiService!.deviceSleepTimeUpdate(
          _deviceProvider!.device!.deviceName,
          _sleepStartTime,
          _sleepEndTime,
        );
      } else {
        success = await _deviceApiService!.deviceSleepTimeUpdate(
          _deviceProvider!.device!.deviceName,
          TimeOfDay(hour: 6, minute: 0),
          TimeOfDay(hour: 0, minute: 0),
        );
      }

      if (!mounted) return;

      if (success) {
        // 如果接口调用成功，更新设备状态
        _deviceProvider!.updateDevice({
          'isSleep': value,
          'sleepStartTime': _sleepStartTime,
          'sleepEndTime': _sleepEndTime,
        });

        setState(() {
          _sleepModeEnabled = value;
        });
      } else {
        // 如果接口调用失败，保持原来的状态
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('更新休眠模式失败')),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('发生错误: $e')),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  // 呼喊声卡片
  Widget _buildVoiceCallCard() {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => AudioRecordScreen()),
        );
      },
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          padding: EdgeInsets.all(20),
          child: Row(
            children: [
              Icon(
                Icons.record_voice_over,
                size: 40,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '呼喊声设置',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 5),
                    Text(
                      '录制和设置您的呼唤声音',
                      style: TextStyle(
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 16),
            ],
          ),
        ),
      ),
    );
  }

  // WIFI设置卡片
  Widget _buildWifiSettingsCard() {
    return GestureDetector(
      onTap: () {
        _showWifiConfigConfirmDialog();
      },
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          padding: EdgeInsets.all(20),
          child: Row(
            children: [
              Icon(
                Icons.wifi,
                size: 40,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '更改设备WIFI',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 5),
                    Text(
                      '配置设备的WIFI连接',
                      style: TextStyle(
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              _isUpdating
                  ? SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).primaryColor,
                        ),
                      ),
                    )
                  : Icon(Icons.arrow_forward_ios, size: 16),
            ],
          ),
        ),
      ),
    );
  }

  // 处理WiFi配置更新
  Future<void> _handleWifiConfigUpdate() async {
    // 检查widget是否仍然活跃
    if (!mounted) return;

    // 显示加载指示器
    setState(() {
      _isUpdating = true;
    });

    try {
      final device = _deviceProvider?.device;
      if (device == null) {
        throw Exception('设备不存在');
      }

      if (_deviceApiService == null) {
        throw Exception('设备服务不可用');
      }

      final success = await _deviceApiService!.deviceWifiConfigUpdate(
        device.deviceName,
      );

      // 在异步操作后再次检查widget状态
      // if (!mounted) return;

      if (success) {
        // 如果成功，跳转到设备连接页面
        _deviceProvider?.updateDevice({
          'wifiConfig': false,
        });
        Navigator.pushNamed(context, Routes.deviceScan);
      } else {
        // 如果失败，显示错误提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('WiFi配置更新失败')),
        );
      }
    } catch (e) {
      // 在异步操作后再次检查widget状态
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('发生错误: $e')),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  // 显示WiFi配置确认对话框
  void _showWifiConfigConfirmDialog() {
    final device = _deviceProvider?.device;
    if (device == null) {
      throw Exception('设备不存在');
    }

    // 如果正在自动配网，则提示用户
    if (_deviceConnectAutoService!.isBLEWorking) {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('APP正在尝试自动连接家庭WiFi，请稍后再试！')));
      return;
    }

    if (_deviceConnectAutoService!.currentState ==
        AutoConnectState.homeWifiConfig) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('自动配网模式'),
            content: Text('APP会尝试自动连接家庭WIFI网络，是否取消自动配网？'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(); // 关闭对话框
                },
                child: Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(); // 关闭对话框
                  // 使用WidgetsBinding.instance.addPostFrameCallback确保在下一帧执行
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _deviceConnectAutoService!.stopMonitoring();
                  });
                },
                child: Text('确认'),
              ),
            ],
          );
        },
      );
      return;
    }

    // 如果设备没有配置WiFi或者设备不在线，则跳转到设备连接页面
    if (!device.wifiConfig || !device.isOnline || device.deviceName.isEmpty) {
      Navigator.pushNamed(context, Routes.deviceScan);
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('WiFi配置确认'),
          content: Text('点击该操作将删除原来的WiFi配置！确认是否进行？'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 关闭对话框
              },
              child: Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 关闭对话框
                // 使用WidgetsBinding.instance.addPostFrameCallback确保在下一帧执行
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _handleWifiConfigUpdate();
                });
              },
              child: Text('确认'),
            ),
          ],
        );
      },
    );
  }

  // 验证休眠时间
  bool _validateSleepTime(TimeOfDay startTime, TimeOfDay endTime) {
    // 检查时间范围是否在 00:00 到 06:59 之间
    bool isStartTimeValid = (startTime.hour >= 0 && startTime.hour <= 6) &&
        !(startTime.hour == 6 && startTime.minute > 59);
    bool isEndTimeValid = (endTime.hour >= 0 && endTime.hour <= 6) &&
        !(endTime.hour == 6 && endTime.minute > 59);

    // 检查开始时间是否早于结束时间
    bool isTimeOrderValid = startTime.hour < endTime.hour ||
        (startTime.hour == endTime.hour && startTime.minute < endTime.minute);

    return isStartTimeValid && isEndTimeValid && isTimeOrderValid;
  }

  // 处理时间更新
  Future<void> _handleTimeUpdate(TimeOfDay startTime, TimeOfDay endTime) async {
    if (!mounted) return;

    // 验证时间设置
    if (!_validateSleepTime(startTime, endTime)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('目前设备休眠时间仅支持00:00 到06:59，休眠开始时间不能晚于结束时间')),
      );
      return;
    }

    setState(() {
      _isUpdating = true;
    });

    // 保存之前的时间，以便在更新失败时恢复
    final previousStartTime = _sleepStartTime;
    final previousEndTime = _sleepEndTime;

    // 先更新UI显示新选择的时间
    setState(() {
      _sleepStartTime = startTime;
      _sleepEndTime = endTime;
    });

    // 调用API更新设备休眠时间
    if (_deviceProvider?.device != null && _deviceApiService != null) {
      try {
        final success = await _deviceApiService!.deviceSleepTimeUpdate(
          _deviceProvider!.device!.deviceName,
          startTime,
          endTime,
        );

        if (!mounted) return;

        if (success) {
          // 如果成功，更新设备模型
          _deviceProvider!.updateDevice({
            'sleepStartTime': startTime,
            'sleepEndTime': endTime,
            'isSleep': _sleepModeEnabled, // 保持当前休眠模式状态
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('休眠时间更新成功')),
          );
        } else {
          // 如果失败，恢复之前的时间设置
          setState(() {
            _sleepStartTime = previousStartTime;
            _sleepEndTime = previousEndTime;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('休眠时间更新失败')),
          );
        }
      } catch (e) {
        if (!mounted) return;

        // 发生异常时，恢复之前的时间设置
        setState(() {
          _sleepStartTime = previousStartTime;
          _sleepEndTime = previousEndTime;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('发生错误: $e')),
        );
      } finally {
        if (mounted) {
          setState(() {
            _isUpdating = false;
          });
        }
      }
    } else {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  // 显示时间选择器对话框
  void _showTimePicker() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return CustomTimePickerDialog(
          initialStartTime: _sleepStartTime,
          initialEndTime: _sleepEndTime,
          onTimeSelected: (startTime, endTime) {
            // 使用WidgetsBinding.instance.addPostFrameCallback确保在下一帧执行
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _handleTimeUpdate(startTime, endTime);
            });
          },
        );
      },
    );
  }
}
