import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../constants/constants.dart';
import '../widgets/pet_info_card.dart';
import '../widgets/no_pet_card.dart';
import '../widgets/pet_loading_card.dart';
import '../providers/auth_provider.dart';
import '../providers/device_provider.dart';
import 'user_profile_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      authProvider.refreshPetInfo();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('个人资料'),
        elevation: 1,
      ),
      backgroundColor: Colors.grey[200],
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 16),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              alignment: Alignment.centerLeft,
              child: Text(
                '爱宠信息',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: 4),
            Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                return _buildPetCard();
              },
            ),
            const SizedBox(height: 24),
            _buildMenuSection(),
            const SizedBox(height: 24),
            _buildLogoutButton(),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildPetCard() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (authProvider.isPetLoading) {
      return const PetLoadingCard();
    }

    if (authProvider.currentPet == null) {
      return NoPetCard(onTap: _navigateToAddPet);
    } else {
      return PetInfoCard(
        pet: authProvider.currentPet!,
        onTap: _navigateToPetDetail,
      );
    }
  }

  void _navigateToAddPet() async {
    final result = await Navigator.of(context).pushNamed(Routes.petInfoInput);
    if (result != null) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.refreshPetInfo(); // 重新加载宠物信息
    }
  }

  void _navigateToPetDetail() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final result = await Navigator.of(context)
        .pushNamed(Routes.petDetail, arguments: authProvider.currentPet);
    if (result != null) {
      await authProvider.refreshPetInfo(); // 重新加载宠物信息
    }
  }

  // 构建菜单部分
  Widget _buildMenuSection() {
    // 使用Selector精确监听设备是否存在，避免Consumer的副作用
    return Selector<DeviceProvider, bool>(
      selector: (context, deviceProvider) => deviceProvider.device != null,
      builder: (context, hasDevice, child) {
        return _buildMenuList(hasDevice);
      },
    );
  }

  // 构建菜单列表
  Widget _buildMenuList(bool hasDevice) {
    final menuItems = [
      {
        'icon': Icons.person,
        'title': '个人资料',
        'onTap': () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => UserProfileScreen(),
            ),
          );
        },
      },
      // 只有当设备存在时才显示项圈解绑菜单项
      if (hasDevice)
        {
          'icon': Icons.link_off,
          'title': '项圈解绑',
          'onTap': () => _showCollarUnbindWarning(),
        },
      {
        'icon': Icons.help_outline,
        'title': '帮助与客服',
        'onTap': () {
          Navigator.pushNamed(context, Routes.customerService);
        },
      },
      {
        'icon': Icons.info_outline,
        'title': '关于',
        'onTap': () {
          Navigator.pushNamed(context, Routes.about);
        },
      },
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: EdgeInsets.zero,
          itemCount: menuItems.length,
          separatorBuilder: (context, index) => Divider(
            height: 1,
            color: Colors.grey[200],
            indent: 64,
            endIndent: 16,
          ),
          itemBuilder: (context, index) {
            final item = menuItems[index];
            return ListTile(
              contentPadding: const EdgeInsets.symmetric(horizontal: 20),
              leading: Icon(
                item['icon'] as IconData,
                color: Colors.grey[600],
                size: 24,
              ),
              title: Text(
                item['title'] as String,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              trailing: Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 14,
              ),
              onTap: item['onTap'] as VoidCallback,
            );
          },
        ),
      ),
    );
  }

  // 构建退出登录按钮
  Widget _buildLogoutButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: () => _showLogoutConfirmDialog(),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orangeAccent,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
            elevation: 2,
          ),
          child: const Text(
            '退出登录',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  // 显示退出登录确认对话框
  void _showLogoutConfirmDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('确认退出'),
          content: const Text('您确定要退出登录吗？'),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                '取消',
                style: TextStyle(
                  color: Colors.grey[600],
                ),
              ),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _logout();
              },
              child: Text(
                '确认',
                style: TextStyle(
                  color: Colors.red[400],
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // 执行退出登录
  Future<void> _logout() async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.logout();

      // 跳转到登录页面
      Navigator.of(context).pushNamedAndRemoveUntil(
        Routes.login,
        (route) => false,
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('退出登录失败: $e'),
          backgroundColor: Colors.red[400],
        ),
      );
    }
  }

  // ==================== 项圈解绑相关方法 ====================

  /// 显示项圈解绑警告确认对话框
  void _showCollarUnbindWarning() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            '警告',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: const Text(
            '解除绑定前请确保你的项圈在线，解除绑定后您的项圈所有数据将丢失，请谨慎操作！',
            style: TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                '取消',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (mounted) {
                  _performCollarUnbind();
                }
              },
              child: Text(
                '确定',
                style: TextStyle(
                  color: Colors.red[400],
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 执行项圈解绑操作
  Future<void> _performCollarUnbind() async {
    try {
      final deviceProvider =
          Provider.of<DeviceProvider>(context, listen: false);

      final result = await deviceProvider.unbindCollar();
      // 检查Widget是否仍然挂载再显示结果对话框
      _showResultDialog(
        title: result['success'] ? '成功' : '失败',
        content: result['message'],
        isSuccess: result['success'],
      );
    } catch (e) {
      _showResultDialog(
        title: '失败',
        content: '解绑过程中发生错误: ${e.toString()}',
        isSuccess: false,
      );
    }
  }

  /// 显示结果对话框
  void _showResultDialog({
    required String title,
    required String content,
    required bool isSuccess,
  }) {
    final globalContext = navigatorKey.currentContext;
    showDialog(
      context: globalContext!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: isSuccess ? Colors.green[600] : Colors.red[600],
            ),
          ),
          content: Text(
            content,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                '确定',
                style: TextStyle(
                  color: isSuccess ? Colors.green[600] : Colors.red[600],
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
