import 'package:flutter/material.dart';
import '../services/api_service.dart';
import '../utils/toast_utils.dart';
import '../constants/url_form.dart';

/// 服务端设备管理服务
///
/// 服务端设备管理服务，包括设备状态查询（是否激活）、设备绑定/解绑用户、app和服务端设备信息同步、
/// 查询设备是否休眠或者掉线、向设备发送控制指令、设备音频更新、设备固件升级。
///
class DeviceApiService {
  final ApiService _apiService;

  DeviceApiService(this._apiService);

  // 云端上查询设备是否激活
  Future<Map<String, dynamic>> deviceInfo(String deviceName) async {
    final body = {'device_name': deviceName};
    final jsonResponse = await _apiService.postRequest(
      UrlFormat.DeviceInfo,
      body,
    );

    if (jsonResponse['code'] == 200) {
      return {'Success': true};
    } else if (jsonResponse['code'] == 601) {
      return {
        'Success': false,
        'errorCode': 'iot.connect.error',
        'errorMessage': '注册服务器异常，请重试',
      };
    } else if (jsonResponse['code'] == 602) {
      return {
        'Success': false,
        'errorCode': 'iot.register.fail',
        'errorMessage': '设备注册失败，请联系客服解决',
      };
    } else {
      return {
        'Success': false,
        'errorCode': 'iot.connect.fail',
        'errorMessage': '设备注册失败，请联系客服解决',
      };
    }
  }

  // 设备绑定用户
  Future<Map<String, dynamic>> deviceBindUser(dynamic device_data) async {
    final body = device_data;
    final jsonResponse = await _apiService.postRequest(
      UrlFormat.DeviceBindUser,
      body,
    );
    if (jsonResponse['code'] == 200) {
      return {
        'Success': true,
        'productId': jsonResponse['data']['product_id'],
      };
    } else {
      return {
        'Success': false,
        'errorCode': 'device.bind.fail',
        'errorMessage': jsonResponse['message'],
      };
    }
  }

  // 设备解绑用户
  Future<bool> deviceUnbindUser(String deviceName) async {
    final body = {
      'device_name': deviceName,
    };
    final jsonResponse = await _apiService.postRequest(
      UrlFormat.DeviceUnbindUser,
      body,
    );
    if (jsonResponse['code'] == 200) {
      return true;
    } else {
      // ToastUtils.showError('设备解绑用户: ${jsonResponse['message']}');
      return false;
    }
  }

  // 设备信息同步数据库
  Future<Map<String, dynamic>> deviceSyncInfo() async {
    final jsonResponse =
        await _apiService.postRequest(UrlFormat.DeviceSyncInfo, {});
    final data = jsonResponse['data'];
    final code = jsonResponse['code'];
    if (code == 200) {
      return data;
    } else if (code == 609) {
      return {};
    } else {
      ToastUtils.showError('设备信息同步数据库: ${jsonResponse['message']}');
      // 抛出异常
      throw Exception(jsonResponse['message']);
    }
  }

  // 查询设备是否休眠或者掉线
  // Future<dynamic> deviceSleepOrOffline(String deviceName) async {
  //   final body = {
  //     'device_name': deviceName,
  //   };
  //   final jsonResponse = await _apiService.postRequest(
  //     UrlFormat.DeviceSleepOrOffline,
  //     body,
  //   );
  //   final data = jsonResponse['data'];
  //   final code = data['code'];
  //   if (code == 200) {
  //     return {
  //       'isSleep': data['is_sleep'],
  //       'isOnline': data['is_online'],
  //     };
  //   } else {
  //     ToastUtils.showError('查询设备是否休眠或者掉线: ${jsonResponse['message']}');
  //     // 抛出异常
  //     throw Exception(jsonResponse['message']);
  //   }
  // }

  // 发送设备音频更新命令
  Future<bool> deviceAudioUpdate(String deviceName) async {
    final body = {
      'device_name': deviceName,
      'UPDATE_AUDIO': 'ON',
    };
    final jsonResponse = await _apiService.postRequest(
      UrlFormat.IotSendCommand,
      body,
    );
    if (jsonResponse['code'] == 200) {
      return true;
    } else {
      ToastUtils.showError('设备音频更新: ${jsonResponse['message']}');
      return false;
    }
  }

  // 上传设备音频到服务器上
  Future<bool> deviceAudioUpload(String deviceName, String filePath) async {
    final body = <String, String>{
      'device_name': deviceName,
    };
    final jsonResponse = await _apiService.postFileRequest(
      UrlFormat.DeviceUploadAudio,
      filePath,
      'audio',
      fields: body,
    );
    if (jsonResponse['code'] == 200) {
      return true;
    } else {
      ToastUtils.showError('设备音频上传失败: ${jsonResponse['message']}');
      return false;
    }
  }

  // 查询设备运行状态
  Future<Map<String, dynamic>> deviceRunningStatus(String deviceName) async {
    final body = {
      'device_name': deviceName,
    };
    final jsonResponse = await _apiService.postRequest(
      UrlFormat.DeviceRunningStatus,
      body,
    );
    if (jsonResponse['code'] == 200) {
      return jsonResponse['data'];
    } else {
      // ToastUtils.showError('查询设备运行状态: ${jsonResponse['message']}');
      return {};
    }
  }

  // 设备固件升级
  Future<bool> deviceFirmwareUpgrade(
      String deviceName, String firmwareVersion) async {
    final body = {
      'device_name': deviceName,
      'UPDATE_OTA': firmwareVersion,
    };
    final jsonResponse = await _apiService.postRequest(
      UrlFormat.IotSendCommand,
      body,
    );
    if (jsonResponse['code'] == 200) {
      return true;
    } else {
      ToastUtils.showError('设备固件升级: ${jsonResponse['message']}');
      return false;
    }
  }

  //设备休眠时间更新
  Future<bool> deviceSleepTimeUpdate(String deviceName,
      TimeOfDay sleepStartTime, TimeOfDay sleepEndTime) async {
    final body = {
      'device_name': deviceName,
      'SLEEP_TIME': {
        'start': sleepStartTime.hour.toString(),
        'end': sleepEndTime.hour.toString(),
      },
    };
    final jsonResponse = await _apiService.postRequest(
      UrlFormat.IotSendCommand,
      body,
    );
    if (jsonResponse['code'] == 200) {
      return true;
    } else {
      ToastUtils.showError('设备休眠时间更新: ${jsonResponse['message']}');
      return false;
    }
  }

  // 超声驱虫配置更新
  Future<bool> ultrasonicConfigUpdate(
      String deviceName, String ultrasonicConfigs) async {
    final body = {
      'device_name': deviceName,
      'PWM': ultrasonicConfigs,
    };
    final jsonResponse = await _apiService.postRequest(
      UrlFormat.IotSendCommand,
      body,
    );
    if (jsonResponse['code'] == 200) {
      return true;
    } else {
      ToastUtils.showError('超声驱虫配置更新: ${jsonResponse['message']}');
      return false;
    }
  }

  //设备WIFI配置更新
  Future<bool> deviceWifiConfigUpdate(String deviceName) async {
    final body = {
      'device_name': deviceName,
      'UPDATE_WIFI': 'ON',
    };
    final jsonResponse = await _apiService.postRequest(
      UrlFormat.IotSendCommand,
      body,
    );
    if (jsonResponse['code'] == 200) {
      return true;
    } else {
      ToastUtils.showError('设备WIFI配置更新: ${jsonResponse['message']}');
      return false;
    }
  }
}

///控制设备的传感器
///
///先实现在WIFI环境下，后续再实现蓝牙控制
class DeviceControlService {
  final ApiService _apiService;

  DeviceControlService(this._apiService);

  // 向设备发送控制指令
  Future<dynamic> deviceControl(
      String deviceName, Map<String, dynamic> command) async {
    Map<String, dynamic> body = {
      'device_name': deviceName,
    };
    body.addAll(command);
    final jsonResponse = await _apiService.postRequest(
      UrlFormat.IotSendCommand,
      body,
    );
    return jsonResponse;
  }

  //超声驱虫
  Future<bool> ultrasonicRepellent(
      {required String deviceName, required String command}) async {
    Map<String, dynamic> commandMap = {};
    if (command == 'on') {
      commandMap = {
        'BSP': {
          'ON': ['pwm']
        }
      };
    } else if (command == 'off') {
      commandMap = {
        'BSP': {
          'OFF': ['pwm']
        }
      };
    }
    final jsonResponse = await deviceControl(deviceName, commandMap);
    if (jsonResponse['code'] == 200) {
      return true;
    } else {
      ToastUtils.showError('超声驱虫: ${jsonResponse['message']}');
      return false;
    }
  }

  // 跑马灯
  Future<bool> marqueeLight(
      {required String deviceName, required String command}) async {
    Map<String, dynamic> commandMap = {};
    if (command == 'on') {
      commandMap = {
        'BSP': {
          'ON': ['led']
        }
      };
    } else if (command == 'off') {
      commandMap = {
        'BSP': {
          'OFF': ['led']
        }
      };
    }
    var jsonResponse = await deviceControl(deviceName, commandMap);
    if (jsonResponse['code'] == 200) {
      return true;
    } else {
      ToastUtils.showError('跑马灯: ${jsonResponse['message']}');
      return false;
    }
  }

  // 呼叫
  Future<bool> call(
      {required String deviceName, required String command}) async {
    Map<String, dynamic> commandMap = {};
    var jsonResponse;
    if (command == 'on') {
      commandMap = {
        'BSP': {
          'ON': ['call']
        }
      };
    } else if (command == 'off') {
      commandMap = {
        'BSP': {
          'OFF': ['call']
        }
      };
    }
    jsonResponse = await deviceControl(deviceName, commandMap);
    if (jsonResponse['code'] == 200) {
      return true;
    } else {
      ToastUtils.showError('呼叫: ${jsonResponse['message']}');
      return false;
    }
  }

  // 遛狗防丢模式
  Future<bool> antiLostMode(
      {required String deviceName, required String command}) async {
    Map<String, dynamic> commandMap = {};
    if (command == 'on') {
      commandMap = {'MUSIC_FLAG': 'ON'};
    } else if (command == 'off') {
      commandMap = {'MUSIC_FLAG': 'OFF'};
    }
    var jsonResponse = await deviceControl(deviceName, commandMap);
    if (jsonResponse['code'] == 200) {
      return true;
    } else {
      ToastUtils.showError('遛狗防丢模式: ${jsonResponse['message']}');
      return false;
    }
  }
}
