import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:provider/provider.dart';

import '../services/blufi_service.dart';
import '../providers/device_provider.dart';
import '../constants/constants.dart';
import '../utils/app_logger.dart';
import '../services/wifi_scan_service.dart';
import '../models/wifi.dart';

// WiFi信息类
class WifiInfo {
  final String ssid;
  final int signalStrength;
  final String source; // 'phone' 或 'device'

  WifiInfo({
    required this.ssid,
    required this.signalStrength,
    required this.source,
  });
}

// DeviceConnectScreen 负责显示当前已连接的蓝牙设备列表。
class DeviceConnectScreen extends StatefulWidget {
  @override
  _DeviceConnectScreenState createState() => _DeviceConnectScreenState();
}

class _DeviceConnectScreenState extends State<DeviceConnectScreen> {
  final _formKey = GlobalKey<FormState>();

  //家庭WIFI
  final TextEditingController _accountController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
// 手机热点
  final TextEditingController _accountHostpotController =
      TextEditingController();
  final TextEditingController _passwordHostpotController =
      TextEditingController();

  Map<String, dynamic> scanResult = Map<String, dynamic>();
  bool _isLoading = false;
  bool _isConnected = false;
  bool _isConfigured = false;
  bool pwdShow = false;
  String bleMac = '';
  String deviceName = '';

  List<WifiInfo> wifiList = [];
  Map<String, dynamic> deviceData = {};

  late DeviceProvider _deviceProvider;
  late BlufiService _blufiService;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _blufiService = Provider.of<BlufiService>(context, listen: false);
      _deviceProvider = Provider.of<DeviceProvider>(context, listen: false);
      _blufiCallback();
      _connectDeviceBle();
    });
  }

  @override
  void dispose() {
    super.dispose();
    _blufiService.dispose();
  }

  void _blufiCallback() {
    _blufiService.onConnectionStateChanged = (state) {
      switch (state) {
        case BluetoothConnectionState.disconnected:
          if (_isConnected && _isConfigured) {
            _isConnected = false;
            // 配网成功连接WIFI成功会自动断开蓝牙，以蓝牙断开标志配网成功
            //保存热点信息
            wifiConfig.saveConfig(wifiConfig(
              homeSsid: _accountController.text,
              homePassword: _passwordController.text,
              hotspotSsid: _accountHostpotController.text,
              hotspotPassword: _passwordHostpotController.text,
            ));

            setState(() {
              _isLoading = false;
              if (_deviceProvider.device == null) {
                deviceData['wifiName'] = _accountController.text;
                deviceData['wifiConfig'] = true;
                deviceData['bleMac'] = bleMac;
                deviceData['deviceName'] = deviceName;
                _deviceProvider.createDevice(deviceData);
                //设备WiFi连接成功后转跳到设备注册界面,到云端去查询设备是否激活
                Navigator.of(context).pushNamedAndRemoveUntil(
                    Routes.deviceRegistration, (Route<dynamic> route) => false);
              } else if (_deviceProvider.device!.deviceName == deviceName) {
                _deviceProvider.updateDevice({
                  'wifiName': _accountController.text,
                  'wifiConfig': true,
                });
                // 导航回到主页面并显示设备管理tab（索引2）
                Navigator.of(context)
                    .pushReplacementNamed(Routes.home, arguments: 2);
              }
            });
          }
          break;
        case BluetoothConnectionState.connected:
          break;
        default:
          break;
      }
    };
  }

  //连接设备蓝牙
  Future<void> _connectDeviceBle() async {
    bleMac = ModalRoute.of(context)!.settings.arguments as String;
    try {
      _isConnected = false;
      _isConfigured = false;

      bool hasDevice = await _blufiService.scanForDevice(bleMac);
      if (!hasDevice) {
        hasDevice = await _blufiService.scanForDevice(bleMac);
      }
      if (!hasDevice) {
        throw Exception('没有办法连接项圈蓝牙');
      }

      final connected = await _blufiService.connectDeviceWithListener();
      if (connected) {
        _isConnected = true;
        deviceName = await _blufiService.sendCustomData('get_devicename');
        // 连接成功后，BluFi数据监听服务已经自动设置
        await Future.delayed(const Duration(milliseconds: 1));
        await _blufiService.sendCustomData('ws2812_on');
        await Future.delayed(const Duration(milliseconds: 1));
        _isConfigured = await _blufiService.getWiFiStatus();
        if (!_isConfigured) {
          throw Exception('项圈蓝牙无反应！');
        }
      }
    } catch (e, s) {
      AppLogger.error("连接蓝牙无响应: $e", error: e, stackTrace: s);
      _blufiService.disconnectDevice();
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('设备蓝牙连接失败! 请检查设备。')));
    }
  }

  //使用手机上的WIFI功能来扫描周围的路由器
  Future<void> _phoneWifiScan() async {
    try {
      final wifiService = WifiScanService();

      // 检查权限
      final hasPermission = await wifiService.hasLocationPermission();
      if (!hasPermission) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('需要位置权限才能扫描WiFi网络')),
        );
        return;
      }

      setState(() {
        if (wifiList.length != 0) {
          wifiList.clear();
        }
        _isLoading = true;
      });

      // 扫描WiFi网络
      final accessPoints = await wifiService.scanWifiNetworks();

      // 将扫描结果添加到wifiList（只添加非空的SSID）
      for (final ap in accessPoints) {
        if (ap.ssid.isNotEmpty &&
            !wifiList.any((wifi) => wifi.ssid == ap.ssid)) {
          wifiList.add(WifiInfo(
            ssid: ap.ssid,
            signalStrength: ap.level,
            source: 'phone',
          ));
        }
      }

      setState(() {
        _isLoading = false;
      });

      // 显示WiFi列表
      _showWifiList();

      AppLogger.info('手机WiFi扫描完成，找到${wifiList.length}个网络');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      AppLogger.error('手机WiFi扫描失败：$e', error: e);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('WiFi扫描失败: $e')),
        );
      }
    }
  }

  // 连接设备wifi
  Future<void> _connectDeviceWifi() async {
    if (!_isConnected) {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('无法连接项圈，请退出重试！')));
      return;
    }

    if (deviceName.isEmpty) {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('设备名称获取失败，请退出重试！')));
      return;
    }

    if (_deviceProvider.device != null &&
        _deviceProvider.device!.deviceName != deviceName) {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('账号已经存在项圈，请先解除绑定，再进行绑定')));
      return;
    }
    // 先验证各个表单字段是否合法
    if ((_formKey.currentState as FormState).validate()) {
      try {
        setState(() {
          _isLoading = true;
        });
        _isConfigured = await _blufiService.sendSSID(_accountController.text);
        await Future.delayed(const Duration(milliseconds: 500));
        _isConfigured &=
            await _blufiService.sendPassword(_passwordController.text);
        await Future.delayed(const Duration(milliseconds: 500));
        _isConfigured &= await _blufiService.sendConnectCommand();
        if (!_isConfigured) {
          throw Exception('项圈配置不成功');
        }
      } catch (e, s) {
        AppLogger.error("配置项圈WiFi发生错误: $e", error: e, stackTrace: s);
        setState(() {
          _isLoading = false;
        });
        // 可以添加更多的错误处理逻辑，如显示 SnackBar 给用户
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text('配置设备时出错: $e，请退出重试')));
      }
    }
  }

  // 弹窗显示wifi列表
  void _showWifiList() {
    showDialog(
      context: context,
      barrierDismissible: true, // 点击外部可关闭
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('可用WiFi列表'),
          content: Container(
            width: double.maxFinite,
            height: 300,
            child: wifiList.isEmpty
                ? Center(
                    child: Text('未发现可用的WiFi网络'),
                  )
                : ListView.builder(
                    itemCount: wifiList.length,
                    itemBuilder: (context, index) {
                      final wifi = wifiList[index];
                      return ListTile(
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        leading: Icon(
                          _getWifiIcon(wifi.signalStrength),
                          color: _getSignalColor(wifi.signalStrength),
                          size: 24,
                        ),
                        title: Text(
                          wifi.ssid,
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 16,
                          ),
                        ),
                        trailing: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              '${wifi.signalStrength} dBm',
                              style: TextStyle(
                                color: _getSignalColor(wifi.signalStrength),
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              _getSignalStrengthText(wifi.signalStrength),
                              style: TextStyle(
                                color: _getSignalColor(wifi.signalStrength),
                                fontSize: 10,
                              ),
                            ),
                          ],
                        ),
                        onTap: () {
                          _accountController.text = wifi.ssid;
                          Navigator.pop(context); // 关闭弹窗
                        },
                        shape: Border(
                          bottom: BorderSide(
                            color: Colors.grey.shade300,
                            width: 1.0,
                          ),
                        ),
                      );
                    },
                  ),
          ),
        );
      },
    );
  }

  // 根据信号强度获取WiFi图标
  IconData _getWifiIcon(int signalStrength) {
    if (signalStrength >= -50) {
      return Icons.wifi_outlined;
    } else if (signalStrength >= -60) {
      return Icons.wifi_outlined;
    } else if (signalStrength >= -70) {
      return Icons.wifi_2_bar_outlined;
    } else if (signalStrength >= -80) {
      return Icons.wifi_1_bar_outlined;
    } else if (signalStrength >= -90) {
      return Icons.wifi_off_outlined;
    } else {
      return Icons.wifi_off_outlined;
    }
  }

  // 根据信号强度获取颜色
  Color _getSignalColor(int signalStrength) {
    if (signalStrength >= -50) {
      return Colors.green;
    } else if (signalStrength >= -70) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  // 获取信号强度文字描述
  String _getSignalStrengthText(int signalStrength) {
    if (signalStrength >= -50) {
      return '极强';
    } else if (signalStrength >= -60) {
      return '很强';
    } else if (signalStrength >= -70) {
      return '强';
    } else if (signalStrength >= -80) {
      return '中等';
    } else if (signalStrength >= -90) {
      return '弱';
    } else {
      return '极弱';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('设备WiFi配网')),
      resizeToAvoidBottomInset: true, // 允许页面在键盘弹出时调整大小
      body: Stack(
        fit: StackFit.expand,
        children: [
          SingleChildScrollView(
            // 添加滚动支持
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                // 移除 Center，使用 Column 直接布局
                crossAxisAlignment: CrossAxisAlignment.stretch, // 改为拉伸对齐
                children: [
                  SizedBox(height: 20),
                  TextFormField(
                    autofocus: false,
                    controller: _accountController,
                    decoration: InputDecoration(
                      labelText: '家庭WIFI账号',
                      hintText: 'wifi账号',
                      prefixIcon: Icon(Icons.wifi),
                      suffixIcon: IconButton(
                          icon: Icon(Icons.arrow_forward_ios),
                          onPressed: () async {
                            await _phoneWifiScan();
                          } //扫描WiFi,
                          ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) return '请输入家庭wifi账号';
                      return null;
                    },
                  ),
                  SizedBox(height: 15),
                  TextFormField(
                    autofocus: false,
                    controller: _passwordController,
                    decoration: InputDecoration(
                      labelText: '家庭WIFI密码',
                      hintText: 'wifi密码',
                      prefixIcon: Icon(Icons.lock),
                      suffixIcon: IconButton(
                        icon: Icon(
                            pwdShow ? Icons.visibility : Icons.visibility_off),
                        onPressed: () {
                          setState(() {
                            pwdShow = !pwdShow;
                          });
                        },
                      ),
                    ),
                    obscureText: !pwdShow,
                    validator: (value) {
                      if (value == null || value.isEmpty) return '请输入家庭wifi密码';
                      return null;
                    },
                  ),
                  SizedBox(height: 30),

                  TextFormField(
                    autofocus: false,
                    controller: _accountHostpotController,
                    decoration: InputDecoration(
                      labelText: '手机热点账号',
                      hintText: '热点账号',
                      prefixIcon: Icon(Icons.wifi),
                      // suffixIcon: IconButton(
                      //     icon: Icon(Icons.arrow_forward_ios),
                      //     onPressed: () async {
                      //       await _phoneWifiScan();
                      //     } //扫描WiFi,
                      //     ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) return '请输入手机热点账号';
                      return null;
                    },
                  ),
                  SizedBox(height: 15),
                  TextFormField(
                    autofocus: false,
                    controller: _passwordHostpotController,
                    decoration: InputDecoration(
                      labelText: '手机热点密码',
                      hintText: '热点密码',
                      prefixIcon: Icon(Icons.lock),
                      suffixIcon: IconButton(
                        icon: Icon(
                            pwdShow ? Icons.visibility : Icons.visibility_off),
                        onPressed: () {
                          setState(() {
                            pwdShow = !pwdShow;
                          });
                        },
                      ),
                    ),
                    obscureText: !pwdShow,
                    validator: (value) {
                      if (value == null || value.isEmpty) return '请输入手机热点密码';
                      return null;
                    },
                  ),

                  Padding(
                    padding: const EdgeInsets.only(top: 25),
                    child: ConstrainedBox(
                      constraints: BoxConstraints.expand(height: 55.0),
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orangeAccent, // 设置背景色
                          foregroundColor: Colors.white, // 设置文字颜色
                        ),
                        onPressed: _connectDeviceWifi,
                        child: Text('确定'),
                      ),
                    ),
                  ),
                  // if (_isLoading)  CircularProgressIndicator(),

                  // 添加说明文字
                  Container(
                    margin: const EdgeInsets.only(top: 50),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info_outline,
                                color: Colors.blue, size: 16),
                            SizedBox(width: 6),
                            Text(
                              '网络配置说明',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue.shade700,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 8),
                        Text(
                          '• 家庭WIFI：为项圈在室内时提供网络连接\n• 手机热点：为宠物外出时提供网络连接',
                          style: TextStyle(
                            color: Colors.grey.shade700,
                            fontSize: 13,
                            height: 1.4,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (_isLoading)
            AbsorbPointer(
              absorbing: true,
              child: Container(
                color: Colors.black.withOpacity(0.5), // 半透明背景
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
