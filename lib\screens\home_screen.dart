import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/device_provider.dart';
import '../screens/health_screen.dart';
import '../screens/device_control_screen.dart';
import '../screens/device_manage_screen.dart';
import '../screens/profile_screen.dart';

class HomeScreen extends StatefulWidget {
  final int? initialIndex;

  const HomeScreen({super.key, this.initialIndex});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late int _currentIndex;
  final List<Widget> _children = [
    HealthScreen(),
    DeviceControlScreen(),
    DeviceManageScreen(),
    ProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    // 使用构造参数初始化索引，如果没有传入则默认为0
    _currentIndex = widget.initialIndex ?? 0;

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      final deviceProvider =
          Provider.of<DeviceProvider>(context, listen: false);
      deviceProvider.initDevices(true);
    });
  }

  void onTabTapped(int index) {
    // 如果点击的是AI助手（索引2），导航到独立的聊天页面
    if (index == 2) {
      Navigator.pushNamed(context, '/chatbot');
      return;
    }

    // 调整其他tab的索引（因为移除了中间的聊天tab）
    int adjustedIndex = index;
    if (index > 2) {
      adjustedIndex = index - 1;
    }

    setState(() {
      _currentIndex = adjustedIndex;
    });
  }

  @override
  Widget build(BuildContext context) {
    // 加载设备的属性
    return Scaffold(
      body: _children[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        onTap: onTabTapped, // new
        currentIndex: _currentIndex >= 2 ? _currentIndex + 1 : _currentIndex,
        fixedColor: const Color.fromARGB(255, 243, 117, 33),
        type: BottomNavigationBarType.fixed,
        iconSize: 20.0,
        backgroundColor: Colors.grey[200],

        items: [
          BottomNavigationBarItem(
            icon: new Icon(Icons.monitor_heart_rounded),
            label: '健康',
          ),
          BottomNavigationBarItem(
            icon: new Icon(Icons.security),
            label: '守护',
          ),
          BottomNavigationBarItem(
            icon: new Icon(Icons.smart_toy),
            label: '小博士',
          ),
          BottomNavigationBarItem(
            icon: new Icon(Icons.watch),
            label: '设备',
          ),
          BottomNavigationBarItem(
            icon: new Icon(Icons.person),
            label: '我的',
          ),
        ],
      ),
    );
  }
}
